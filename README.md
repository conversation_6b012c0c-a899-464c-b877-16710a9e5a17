# 银行客户经理移动端工作平台

一个面向银行客户经理的移动端网页应用demo，提供全面的工作管理和客户服务功能。

## 功能特性

### 🏠 工作台
- **每日问候**: 根据时间自动显示个性化的问候语
- **工作统计**: 展示关键业务指标
  - 客户总数
  - 本月新增客户
  - 任务完成率
  - 本月业绩金额
- **常用工具**: 快速访问核心功能
  - 尽调报告
  - 客户管理
  - 产品库
  - 递名片

### 🔍 拓客户
- **白名单**: 优质潜在客户列表管理
- **地图获客**: 基于地理位置的客户挖掘
- **条件搜索**: 多维度客户筛选功能
- **条件订阅**: 客户信息实时推送服务

### 📊 查报告
- **查工商信息**: 企业基本信息查询
- **查公共报告**: 公开市场研究报告
- **查特色报告**: 行业特色分析报告

### 👤 我的
- 个人信息管理
- 应用设置
- 帮助中心
- 退出登录

## 技术特点

- **响应式设计**: 完美适配移动端设备
- **现代化UI**: 采用Material Design设计风格
- **单页面应用**: 流畅的页面切换体验
- **原生JavaScript**: 无需额外框架依赖

## 快速开始

1. 直接打开 `index.html` 文件在浏览器中查看
2. 或使用本地服务器运行:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   
   # Node.js (需要安装http-server)
   npx http-server -p 8000
   ```

3. 访问 `http://localhost:8000`

## 项目结构

```
客户经理2/
├── index.html          # 主页面文件
├── README.md           # 项目说明文档
└── (未来可添加更多资源文件)
```

## 使用说明

1. **底部导航**: 点击底部菜单栏在不同功能模块间切换
2. **工具卡片**: 点击各个功能卡片查看详情（当前为演示弹窗）
3. **搜索功能**: 在拓客户和查报告页面使用搜索框
4. **响应式交互**: 所有交互元素都有悬停效果和点击反馈

## 浏览器兼容性

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 后续开发建议

1. 添加后端API接口
2. 实现用户登录认证
3. 集成真实数据
4. 添加更多业务功能模块
5. 优化移动端性能
6. 添加离线功能支持

## 许可证

MIT License
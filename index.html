<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行客户经理工作平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 480px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
            position: relative;
            padding-bottom: 60px;
        }
        
        .header {
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 20px;
            margin-bottom: 5px;
        }
        
        .greeting {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 20px;
        }
        
        .section {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1976d2;
            border-left: 4px solid #1976d2;
            padding-left: 12px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .tool-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .tool-card:hover {
            transform: translateY(-2px);
        }
        
        .tool-icon {
            font-size: 24px;
            margin-bottom: 8px;
            color: #1976d2;
        }
        
        .tool-name {
            font-size: 14px;
            font-weight: 500;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-around;
            padding: 12px 0;
            max-width: 480px;
            margin: 0 auto;
        }
        
        .nav-item {
            text-align: center;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .nav-item.active {
            background-color: #e3f2fd;
            color: #1976d2;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .search-box {
            background: white;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 16px;
        }
        
        .search-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .feature-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .feature-item {
            padding: 16px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .feature-item:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 工作台页面 -->
        <div id="dashboard" class="page active">
            <div class="header">
                <h1>银行客户经理工作平台</h1>
                <div class="greeting" id="greeting">早上好，客户经理！</div>
            </div>
            
            <div class="content">
                <!-- 工作统计 -->
                <div class="section">
                    <div class="section-title">工作统计</div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">128</div>
                            <div class="stat-label">客户总数</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">24</div>
                            <div class="stat-label">本月新增</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">86%</div>
                            <div class="stat-label">完成率</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">¥5.2M</div>
                            <div class="stat-label">本月业绩</div>
                        </div>
                    </div>
                </div>
                
                <!-- 常用工具 -->
                <div class="section">
                    <div class="section-title">常用工具</div>
                    <div class="tools-grid">
                        <div class="tool-card" onclick="alert('打开尽调报告')">
                            <div class="tool-icon">📋</div>
                            <div class="tool-name">尽调报告</div>
                        </div>
                        <div class="tool-card" onclick="alert('打开客户管理')">
                            <div class="tool-icon">👥</div>
                            <div class="tool-name">客户管理</div>
                        </div>
                        <div class="tool-card" onclick="alert('打开产品库')">
                            <div class="tool-icon">📦</div>
                            <div class="tool-name">产品库</div>
                        </div>
                        <div class="tool-card" onclick="alert('打开递名片')">
                            <div class="tool-icon">📇</div>
                            <div class="tool-name">递名片</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 拓客户页面 -->
        <div id="customer-acquisition" class="page">
            <div class="header">
                <h1>拓客户</h1>
                <div class="greeting">寻找新客户</div>
            </div>
            
            <div class="content">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索客户...">
                </div>
                
                <div class="feature-list">
                    <div class="feature-item" onclick="alert('打开白名单')">
                        <strong>白名单</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">优质潜在客户列表</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开地图获客')">
                        <strong>地图获客</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">地理位置客户挖掘</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开条件搜索')">
                        <strong>条件搜索</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">多维度客户筛选</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开条件订阅')">
                        <strong>条件订阅</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">客户信息实时推送</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 查报告页面 -->
        <div id="reports" class="page">
            <div class="header">
                <h1>查报告</h1>
                <div class="greeting">信息查询与分析</div>
            </div>
            
            <div class="content">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="搜索报告...">
                </div>
                
                <div class="feature-list">
                    <div class="feature-item" onclick="alert('打开查工商信息')">
                        <strong>查工商信息</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">企业基本信息查询</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开查公共报告')">
                        <strong>查公共报告</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">公开市场研究报告</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开查特色报告')">
                        <strong>查特色报告</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">行业特色分析报告</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 我的页面 -->
        <div id="profile" class="page">
            <div class="header">
                <h1>我的</h1>
                <div class="greeting">个人中心</div>
            </div>
            
            <div class="content">
                <div class="feature-list">
                    <div class="feature-item" onclick="alert('打开个人信息')">
                        <strong>个人信息</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">查看和修改个人资料</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开设置')">
                        <strong>设置</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">应用偏好设置</div>
                    </div>
                    <div class="feature-item" onclick="alert('打开帮助中心')">
                        <strong>帮助中心</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">使用指南和常见问题</div>
                    </div>
                    <div class="feature-item" onclick="alert('退出登录')">
                        <strong>退出登录</strong>
                        <div style="font-size: 14px; color: #666; margin-top: 4px;">安全退出当前账号</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchPage('dashboard')">
                <div class="nav-icon">💼</div>
                <div class="nav-label">工作台</div>
            </div>
            <div class="nav-item" onclick="switchPage('customer-acquisition')">
                <div class="nav-icon">🔍</div>
                <div class="nav-label">拓客户</div>
            </div>
            <div class="nav-item" onclick="switchPage('reports')">
                <div class="nav-icon">📊</div>
                <div class="nav-label">查报告</div>
            </div>
            <div class="nav-item" onclick="switchPage('profile')">
                <div class="nav-icon">👤</div>
                <div class="nav-label">我的</div>
            </div>
        </div>
    </div>

    <script>
        // 设置问候语
        function setGreeting() {
            const hour = new Date().getHours();
            let greeting = '';
            
            if (hour >= 5 && hour < 12) {
                greeting = '早上好';
            } else if (hour >= 12 && hour < 18) {
                greeting = '下午好';
            } else {
                greeting = '晚上好';
            }
            
            document.getElementById('greeting').textContent = `${greeting}，客户经理！`;
        }
        
        // 切换页面
        function switchPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示选中的页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航激活状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 找到对应的导航项并激活
            const navItems = document.querySelectorAll('.nav-item');
            const pageIndex = ['dashboard', 'customer-acquisition', 'reports', 'profile'].indexOf(pageId);
            if (pageIndex !== -1) {
                navItems[pageIndex].classList.add('active');
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setGreeting();
        });
    </script>
</body>
</html>
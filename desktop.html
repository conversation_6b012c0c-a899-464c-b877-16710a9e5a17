<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银行客户经理管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            color: #333;
            overflow-x: hidden;
        }
        
        .container {
            display: flex;
            height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 260px;
            background: linear-gradient(180deg, #1976d2 0%, #1565c0 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }
        
        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .logo {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .nav-menu {
            padding: 20px 0;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.2s;
            border-left: 3px solid transparent;
        }
        
        .nav-item:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        .nav-item.active {
            background-color: rgba(255,255,255,0.15);
            border-left-color: #42a5f5;
        }
        
        .nav-icon {
            font-size: 18px;
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }
        
        .nav-text {
            font-size: 14px;
            font-weight: 500;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 260px;
            background-color: #f5f7fa;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e6ed;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            margin-right: 16px;
            color: #666;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #1976d2;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background-color: #f8f9fa;
            border-radius: 20px;
            cursor: pointer;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #1976d2, #42a5f5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .content-area {
            padding: 24px;
            min-height: calc(100vh - 80px);
        }
        
        /* 页面内容样式 */
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #e0e6ed;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #1976d2;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn-primary {
            background-color: #1976d2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #1565c0;
        }
        
        .btn-secondary {
            background-color: #f8f9fa;
            color: #666;
            border: 1px solid #e0e6ed;
        }
        
        .btn-secondary:hover {
            background-color: #e9ecef;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .menu-toggle {
                display: block;
            }
            
            .content-area {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">客户经理管理系统</div>
                <div class="subtitle">银行业务管理平台</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item active" onclick="switchPage('dashboard')">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">工作台</span>
                </div>
                <div class="nav-item" onclick="switchPage('leads-pool')">
                    <span class="nav-icon">🎯</span>
                    <span class="nav-text">线索池</span>
                </div>
                <div class="nav-item" onclick="switchPage('assignment')">
                    <span class="nav-icon">📋</span>
                    <span class="nav-text">名单分派</span>
                </div>
                <div class="nav-item" onclick="switchPage('statistics')">
                    <span class="nav-icon">📈</span>
                    <span class="nav-text">活动量统计</span>
                </div>
                <div class="nav-item" onclick="switchPage('manager-management')">
                    <span class="nav-icon">👥</span>
                    <span class="nav-text">客户经理管理</span>
                </div>
                <div class="nav-item" onclick="switchPage('customer-management')">
                    <span class="nav-icon">🏢</span>
                    <span class="nav-text">客户管理</span>
                </div>
            </nav>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" onclick="toggleSidebar()">☰</button>
                    <h1 class="page-title" id="pageTitle">工作台</h1>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <div class="user-avatar">张</div>
                        <span>张经理</span>
                    </div>
                </div>
            </header>
            
            <div class="content-area">
                <!-- 工作台页面 -->
                <div id="dashboard" class="page active">
                    <!-- 数据概览卡片 -->
                    <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 24px;">
                        <div class="stat-card" style="background: linear-gradient(135deg, #1976d2, #42a5f5); color: white; padding: 24px; border-radius: 12px;">
                            <div style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">总线索数</div>
                            <div style="font-size: 32px; font-weight: bold; margin-bottom: 4px;">1,248</div>
                            <div style="font-size: 12px; opacity: 0.8;">较上月 +12.5%</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #388e3c, #66bb6a); color: white; padding: 24px; border-radius: 12px;">
                            <div style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">已分派</div>
                            <div style="font-size: 32px; font-weight: bold; margin-bottom: 4px;">856</div>
                            <div style="font-size: 12px; opacity: 0.8;">分派率 68.6%</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #f57c00, #ffb74d); color: white; padding: 24px; border-radius: 12px;">
                            <div style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">跟进中</div>
                            <div style="font-size: 32px; font-weight: bold; margin-bottom: 4px;">324</div>
                            <div style="font-size: 12px; opacity: 0.8;">转化率 37.9%</div>
                        </div>
                        <div class="stat-card" style="background: linear-gradient(135deg, #7b1fa2, #ba68c8); color: white; padding: 24px; border-radius: 12px;">
                            <div style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">已成交</div>
                            <div style="font-size: 32px; font-weight: bold; margin-bottom: 4px;">123</div>
                            <div style="font-size: 12px; opacity: 0.8;">成交率 14.4%</div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">快速操作</h3>
                        </div>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                            <button class="btn btn-primary" onclick="switchPage('leads-pool')">
                                🎯 查看线索池
                            </button>
                            <button class="btn btn-primary" onclick="switchPage('assignment')">
                                📋 分派名单
                            </button>
                            <button class="btn btn-secondary" onclick="switchPage('statistics')">
                                📈 查看统计
                            </button>
                            <button class="btn btn-secondary" onclick="switchPage('customer-management')">
                                🏢 客户管理
                            </button>
                        </div>
                    </div>

                    <!-- 最近活动 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">最近活动</h3>
                            <button class="btn btn-secondary">查看全部</button>
                        </div>
                        <div class="activity-list">
                            <div style="padding: 12px 0; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">张经理 分派了 15 个线索给 李经理</div>
                                    <div style="font-size: 12px; color: #666; margin-top: 4px;">2024-01-15 14:30</div>
                                </div>
                                <span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">分派</span>
                            </div>
                            <div style="padding: 12px 0; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">王经理 完成了客户 "ABC公司" 的跟进</div>
                                    <div style="font-size: 12px; color: #666; margin-top: 4px;">2024-01-15 13:45</div>
                                </div>
                                <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">跟进</span>
                            </div>
                            <div style="padding: 12px 0; display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div style="font-weight: 500;">系统自动导入了 28 个新线索</div>
                                    <div style="font-size: 12px; color: #666; margin-top: 4px;">2024-01-15 12:00</div>
                                </div>
                                <span style="background: #fff3e0; color: #f57c00; padding: 4px 8px; border-radius: 4px; font-size: 12px;">导入</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 线索池页面 -->
                <div id="leads-pool" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">线索池管理</h3>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-secondary">导入线索</button>
                                <button class="btn btn-primary">新增线索</button>
                            </div>
                        </div>

                        <!-- 筛选工具栏 -->
                        <div style="display: flex; gap: 12px; margin-bottom: 20px; flex-wrap: wrap;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部状态</option>
                                <option>未分派</option>
                                <option>已分派</option>
                                <option>跟进中</option>
                                <option>已成交</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部来源</option>
                                <option>网站注册</option>
                                <option>电话咨询</option>
                                <option>推荐客户</option>
                                <option>展会获取</option>
                            </select>
                            <input type="text" placeholder="搜索客户名称..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; flex: 1; min-width: 200px;">
                            <button class="btn btn-primary">搜索</button>
                        </div>

                        <!-- 线索列表 -->
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">
                                            <input type="checkbox">
                                        </th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">客户名称</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">联系人</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">电话</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">来源</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">状态</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">创建时间</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="leadsTableBody">
                                    <!-- 线索数据将通过JavaScript动态生成 -->
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div style="display: flex; justify-content: between; align-items: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e0e6ed;">
                            <div style="color: #666;">显示 1-10 条，共 248 条记录</div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-secondary">上一页</button>
                                <button class="btn btn-primary">1</button>
                                <button class="btn btn-secondary">2</button>
                                <button class="btn btn-secondary">3</button>
                                <button class="btn btn-secondary">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 名单分派页面 -->
                <div id="assignment" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">名单分派</h3>
                            <button class="btn btn-primary">批量分派</button>
                        </div>

                        <!-- 分派工具栏 -->
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr auto; gap: 12px; margin-bottom: 20px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>选择客户经理</option>
                                <option>张经理 (当前: 45个)</option>
                                <option>李经理 (当前: 38个)</option>
                                <option>王经理 (当前: 52个)</option>
                                <option>刘经理 (当前: 41个)</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>分派规则</option>
                                <option>平均分配</option>
                                <option>按能力分配</option>
                                <option>按地区分配</option>
                                <option>手动指定</option>
                            </select>
                            <input type="number" placeholder="分派数量" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                            <button class="btn btn-primary">执行分派</button>
                        </div>

                        <!-- 客户经理工作量统计 -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px;">
                            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: 600; margin-bottom: 8px;">张经理</div>
                                <div style="font-size: 24px; color: #1976d2; font-weight: bold;">45</div>
                                <div style="font-size: 12px; color: #666;">当前线索数</div>
                            </div>
                            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: 600; margin-bottom: 8px;">李经理</div>
                                <div style="font-size: 24px; color: #1976d2; font-weight: bold;">38</div>
                                <div style="font-size: 12px; color: #666;">当前线索数</div>
                            </div>
                            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: 600; margin-bottom: 8px;">王经理</div>
                                <div style="font-size: 24px; color: #1976d2; font-weight: bold;">52</div>
                                <div style="font-size: 12px; color: #666;">当前线索数</div>
                            </div>
                            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; text-align: center;">
                                <div style="font-weight: 600; margin-bottom: 8px;">刘经理</div>
                                <div style="font-size: 24px; color: #1976d2; font-weight: bold;">41</div>
                                <div style="font-size: 12px; color: #666;">当前线索数</div>
                            </div>
                        </div>

                        <!-- 分派历史 -->
                        <h4 style="margin-bottom: 16px; color: #1976d2;">分派历史</h4>
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">分派时间</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">操作人</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">接收人</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">分派数量</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">分派规则</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">2024-01-15 14:30</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">系统管理员</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">李经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">15</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">平均分配</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">已完成</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">2024-01-15 10:15</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">张经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">王经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">8</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">手动指定</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">已完成</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 活动量统计页面 -->
                <div id="statistics" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">活动量统计</h3>
                            <div style="display: flex; gap: 12px;">
                                <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                    <option>本月</option>
                                    <option>上月</option>
                                    <option>本季度</option>
                                    <option>本年度</option>
                                </select>
                                <button class="btn btn-secondary">导出报表</button>
                            </div>
                        </div>

                        <!-- 统计图表区域 -->
                        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 24px; margin-bottom: 24px;">
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center;">
                                <h4 style="margin-bottom: 16px; color: #1976d2;">线索转化趋势</h4>
                                <div style="height: 200px; display: flex; align-items: center; justify-content: center; color: #666;">
                                    📈 图表区域 (可集成 Chart.js 等图表库)
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                                <h4 style="margin-bottom: 16px; color: #1976d2;">客户经理排行</h4>
                                <div style="space-y: 12px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e0e0e0;">
                                        <span>🥇 王经理</span>
                                        <span style="font-weight: bold; color: #1976d2;">28单</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e0e0e0;">
                                        <span>🥈 李经理</span>
                                        <span style="font-weight: bold; color: #1976d2;">24单</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #e0e0e0;">
                                        <span>🥉 张经理</span>
                                        <span style="font-weight: bold; color: #1976d2;">21单</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0;">
                                        <span>4. 刘经理</span>
                                        <span style="font-weight: bold; color: #1976d2;">18单</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细统计表格 -->
                        <h4 style="margin-bottom: 16px; color: #1976d2;">详细统计</h4>
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">客户经理</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">分派线索</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">跟进次数</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">成交数量</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">转化率</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">业绩金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">王经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">156</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">324</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">28</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">17.9%</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">¥2,840,000</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">李经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">142</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">298</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">24</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">16.9%</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">¥2,160,000</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">张经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">138</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">276</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">21</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">15.2%</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">¥1,890,000</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">刘经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">125</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">245</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">18</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">14.4%</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">¥1,620,000</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 客户经理管理页面 -->
                <div id="manager-management" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户经理管理</h3>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-secondary">导入经理</button>
                                <button class="btn btn-primary">添加经理</button>
                            </div>
                        </div>

                        <!-- 搜索和筛选 -->
                        <div style="display: flex; gap: 12px; margin-bottom: 20px; flex-wrap: wrap;">
                            <input type="text" placeholder="搜索经理姓名..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; flex: 1; min-width: 200px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部部门</option>
                                <option>个人银行部</option>
                                <option>公司银行部</option>
                                <option>投资银行部</option>
                                <option>风险管理部</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部状态</option>
                                <option>在职</option>
                                <option>休假</option>
                                <option>离职</option>
                            </select>
                            <button class="btn btn-primary">搜索</button>
                        </div>

                        <!-- 经理列表 -->
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">姓名</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">工号</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">部门</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">职位</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">联系电话</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">当前线索</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">状态</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #1976d2, #42a5f5); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">王</div>
                                                <span>王经理</span>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">CM001</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">个人银行部</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">高级客户经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">138****1234</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">52</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">在职</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #388e3c, #66bb6a); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">李</div>
                                                <span>李经理</span>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">CM002</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">公司银行部</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">客户经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">139****5678</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">38</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">在职</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #f57c00, #ffb74d); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">张</div>
                                                <span>张经理</span>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">CM003</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">个人银行部</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">客户经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">137****9012</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">45</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">在职</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">详情</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <div style="width: 32px; height: 32px; background: linear-gradient(135deg, #7b1fa2, #ba68c8); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">刘</div>
                                                <span>刘经理</span>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">CM004</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">投资银行部</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">高级客户经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">136****3456</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">41</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #fff3e0; color: #f57c00; padding: 4px 8px; border-radius: 4px; font-size: 12px;">休假</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">编辑</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">详情</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 客户管理页面 -->
                <div id="customer-management" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">客户管理</h3>
                            <div style="display: flex; gap: 12px;">
                                <button class="btn btn-secondary">导入客户</button>
                                <button class="btn btn-primary">添加客户</button>
                            </div>
                        </div>

                        <!-- 搜索和筛选 -->
                        <div style="display: flex; gap: 12px; margin-bottom: 20px; flex-wrap: wrap;">
                            <input type="text" placeholder="搜索客户名称..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; flex: 1; min-width: 200px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部类型</option>
                                <option>个人客户</option>
                                <option>企业客户</option>
                                <option>机构客户</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部状态</option>
                                <option>潜在客户</option>
                                <option>意向客户</option>
                                <option>正式客户</option>
                                <option>流失客户</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option>全部经理</option>
                                <option>王经理</option>
                                <option>李经理</option>
                                <option>张经理</option>
                                <option>刘经理</option>
                            </select>
                            <button class="btn btn-primary">搜索</button>
                        </div>

                        <!-- 客户列表 -->
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background-color: #f8f9fa;">
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">
                                            <input type="checkbox">
                                        </th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">客户名称</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">客户类型</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">联系人</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">联系电话</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">负责经理</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">客户状态</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">最后跟进</th>
                                        <th style="padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <input type="checkbox">
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div>
                                                <div style="font-weight: 500;">ABC科技有限公司</div>
                                                <div style="font-size: 12px; color: #666;">注册资本: 1000万</div>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">企业客户</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">张总</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">138****1234</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">王经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e8f5e8; color: #388e3c; padding: 4px 8px; border-radius: 4px; font-size: 12px;">正式客户</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">2024-01-15</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">跟进</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <input type="checkbox">
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div>
                                                <div style="font-weight: 500;">XYZ贸易公司</div>
                                                <div style="font-size: 12px; color: #666;">注册资本: 500万</div>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">企业客户</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">李总</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">139****5678</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">李经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 4px; font-size: 12px;">意向客户</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">2024-01-14</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">跟进</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <input type="checkbox">
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <div>
                                                <div style="font-weight: 500;">王先生</div>
                                                <div style="font-size: 12px; color: #666;">个人客户</div>
                                            </div>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">个人客户</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">王先生</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">137****9012</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">张经理</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <span style="background: #fff3e0; color: #f57c00; padding: 4px 8px; border-radius: 4px; font-size: 12px;">潜在客户</span>
                                        </td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">2024-01-13</td>
                                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;">查看</button>
                                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;">跟进</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px; padding-top: 20px; border-top: 1px solid #e0e6ed;">
                            <div style="color: #666;">显示 1-10 条，共 156 条记录</div>
                            <div style="display: flex; gap: 8px;">
                                <button class="btn btn-secondary">上一页</button>
                                <button class="btn btn-primary">1</button>
                                <button class="btn btn-secondary">2</button>
                                <button class="btn btn-secondary">3</button>
                                <button class="btn btn-secondary">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function switchPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示选中的页面
            document.getElementById(pageId).classList.add('active');

            // 更新导航激活状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 激活对应的导航项
            event.target.closest('.nav-item').classList.add('active');

            // 更新页面标题
            const titles = {
                'dashboard': '工作台',
                'leads-pool': '线索池',
                'assignment': '名单分派',
                'statistics': '活动量统计',
                'manager-management': '客户经理管理',
                'customer-management': '客户管理'
            };

            document.getElementById('pageTitle').textContent = titles[pageId] || '工作台';
        }

        // 侧边栏切换功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 生成线索池数据
        function generateLeadsData() {
            const leads = [
                {
                    name: 'ABC科技有限公司',
                    contact: '张总',
                    phone: '138****1234',
                    source: '网站注册',
                    status: '未分派',
                    createTime: '2024-01-15'
                },
                {
                    name: 'XYZ贸易公司',
                    contact: '李总',
                    phone: '139****5678',
                    source: '电话咨询',
                    status: '已分派',
                    createTime: '2024-01-14'
                },
                {
                    name: 'DEF制造企业',
                    contact: '王总',
                    phone: '137****9012',
                    source: '推荐客户',
                    status: '跟进中',
                    createTime: '2024-01-13'
                },
                {
                    name: '个人客户-刘先生',
                    contact: '刘先生',
                    phone: '136****3456',
                    source: '展会获取',
                    status: '已成交',
                    createTime: '2024-01-12'
                },
                {
                    name: 'GHI投资公司',
                    contact: '陈总',
                    phone: '135****7890',
                    source: '网站注册',
                    status: '未分派',
                    createTime: '2024-01-11'
                }
            ];

            const tbody = document.getElementById('leadsTableBody');
            if (tbody) {
                tbody.innerHTML = leads.map(lead => `
                    <tr>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                            <input type="checkbox">
                        </td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">${lead.name}</td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">${lead.contact}</td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">${lead.phone}</td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">${lead.source}</td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                            <span style="background: ${getStatusColor(lead.status)}; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${lead.status}</span>
                        </td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">${lead.createTime}</td>
                        <td style="padding: 12px; border-bottom: 1px solid #f0f0f0;">
                            <button class="btn btn-secondary" style="padding: 4px 8px; font-size: 12px;" onclick="viewLead('${lead.name}')">查看</button>
                            <button class="btn btn-primary" style="padding: 4px 8px; font-size: 12px; margin-left: 4px;" onclick="assignLead('${lead.name}')">分派</button>
                        </td>
                    </tr>
                `).join('');
            }
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                '未分派': '#fff3e0; color: #f57c00',
                '已分派': '#e3f2fd; color: #1976d2',
                '跟进中': '#e8f5e8; color: #388e3c',
                '已成交': '#f3e5f5; color: #7b1fa2'
            };
            return colors[status] || '#f8f9fa; color: #666';
        }

        // 查看线索详情
        function viewLead(leadName) {
            alert(`查看线索详情: ${leadName}`);
        }

        // 分派线索
        function assignLead(leadName) {
            alert(`分派线索: ${leadName}`);
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            generateLeadsData();

            // 点击侧边栏外部关闭侧边栏（移动端）
            document.addEventListener('click', function(e) {
                const sidebar = document.getElementById('sidebar');
                const menuToggle = document.querySelector('.menu-toggle');

                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !menuToggle.contains(e.target) &&
                    sidebar.classList.contains('open')) {
                    sidebar.classList.remove('open');
                }
            });

            // 响应式处理
            window.addEventListener('resize', function() {
                const sidebar = document.getElementById('sidebar');
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('open');
                }
            });
        });
    </script>
</body>
</html>
